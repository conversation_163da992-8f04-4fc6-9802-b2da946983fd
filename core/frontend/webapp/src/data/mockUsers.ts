// Simple mock users with just name and userID
export interface SimpleMockUser {
  id: string;
  name: string;
  role?: string; // Optional role field
  legacyId?: string;
}

export const mockUsers: SimpleMockUser[] = [
  {
    legacyId: "4814",
    role: "nurse",
    name: "<PERSON><PERSON>, MARYAM",
    id: "42c8a1d1-f5e4-452e-aa38-6eadbf922ac6",
  },
  {
    legacyId: "10112",
    role: "coordinator",
    name: "<PERSON><PERSON><PERSON>, k<PERSON><PERSON>",
    id: "4bc55e1f-f446-4715-bcd1-cbe8326dab92",
  },
  {
    legacyId: "10109",
    role: "coordinator",
    name: "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>",
    id: "426fc431-3629-46f1-a60e-f5f398959e93",
  },
  {
    legacyId: "10569",
    role: "training specialist",
    name: "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>",
    id: "e9693700-dccf-41a5-bfd4-90f7c9934736",
  },
  {
    legacyId: "10640",
    role: "me engineer",
    name: "<PERSON>, <PERSON><PERSON>",
    id: "a4a8057c-2bc3-4feb-86e4-d80d859d983d",
  },
  {
    legacyId: "10575",
    role: "me engineer",
    name: "Sayad, <PERSON>",
    id: "0543d5fc-ff1d-41a5-8a29-590eaac1bd16",
  },
  {
    legacyId: "74",
    role: "quality supervisor",
    name: "DIANI, NAOUAL",
    id: "270c4ff2-7efa-48d7-b823-114a5385c6c6",
  },
  {
    legacyId: "298",
    role: "quality supervisor",
    name: "EL MIR, DRISS",
    id: "8aa4b6e8-ea00-411e-a459-709c8ea99c6c",
  },
  {
    legacyId: "51",
    role: "quality auditor",
    name: "IBRAHIMI, ITTO ALIA",
    id: "48c090ea-32a3-443a-85ca-5986127bd0e9",
  },
  {
    legacyId: "2632",
    role: "quality auditor",
    name: "NASSIRI, OUAFAA",
    id: "12ecc213-6a8a-4d00-8036-ac3de3214278",
  },
  {
    legacyId: "10797",
    role: "quality engineer",
    name: "El idrissi, Manal",
    id: "e639dcba-1a2a-46cc-8234-037272d5c724",
  },
  {
    legacyId: "4870",
    role: "quality engineer",
    name: "ZIDAL, BOUJEMAA",
    id: "cbbd26b5-b9e7-4f62-a290-676a25720755",
  },
  {
    legacyId: "295",
    role: "team leader",
    name: "EL ATIFI, ABDERRAHIM",
    id: "11e7f215-d7d3-4c0e-aed6-b7c126db7176",
  },
  {
    legacyId: "1257",
    role: "team leader",
    name: "MOUIRI, MUSTAPHA",
    id: "ccc86895-3e25-45f9-9a25-c2fc364edc02",
  },
  {
    legacyId: "291",
    role: "team leader",
    name: "MOUMNI, AZIZ",
    id: "369ba3c2-0c88-4b24-a09b-40ed92064a47",
  },
  {
    legacyId: "112",
    role: "trainer",
    name: "HLIMI, TARIQ",
    id: "e57e0b32-f798-4899-8fd3-0141b6018115",
  },
  {
    legacyId: "80",
    name: "BARI, HAFID",
    role: "trainer",
    id: "f3d69f60-3b9d-4c22-bb31-d18c693bf7f7",
  },
  {
    legacyId: "2221",
    role: "shift leader",
    name: "ELOUARDI, MOHAMMED",
    id: "87fe6a6c-9ef3-4745-9776-82419d118ecf",
  },
  {
    legacyId: "3644",
    role: "shift leader",
    name: "OUBAALI, ADNANE",
    id: "b8c45bec-3bc8-4232-8e24-cf8891de14a2",
  },
  {
    legacyId: "1626",
    role: "DEPARTMENT CLERK",
    name: "LAMRABET, SALMA",
    id: "21378b2c-b018-4521-bf2d-3e5bdf1f4e36",
  },
  {
    legacyId: "10558",
    role: "DEPARTMENT MANAGER",
    name: "El Attaoui, Souheil",
    id: "5770bf7a-8923-4145-bd79-499684f9e869",
  },
  {
    legacyId: "3532",
    role: "TKS AGENT",
    name: "OURIOUR, MARIA",
    id: "46b5ce03-f74d-4ffa-a8cf-cf96bc5edae9",
  },
  {
    legacyId: "4873",
    role: "TRANSPORT AGENT",
    name: "OURRADI, ASMA",
    id: "16db7176-b68a-42b3-a56a-93fd9912c437",
  },
];

// Helper function to get mock user by ID
export const getMockUserById = (userId: string): SimpleMockUser | undefined => {
  return mockUsers.find((user) => user.id === userId);
};

// Helper function to get all available mock users
export const getAllMockUsers = (): SimpleMockUser[] => {
  return mockUsers;
};